#!/bin/bash

# Contact Form Backend Startup Script

echo "Starting Contact Form Backend Server..."

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "Warning: .env file not found!"
    echo "Please copy .env.example to .env and configure your SMTP settings."
    echo "Example:"
    echo "  cp .env.example .env"
    echo "  # Edit .env with your settings"
    exit 1
fi

# Start the server
echo "Starting Flask server..."
python app.py
